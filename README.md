# 🚀 Multi-Agent Web Scraping System

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![<PERSON><PERSON><PERSON><PERSON>](https://img.shields.io/badge/Lang<PERSON>hain-Latest-orange.svg)](https://langchain.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

An advanced, AI-powered web scraping system featuring multiple specialized agents, intelligent orchestration, and both CLI and Web interfaces. Built with LangChain for AI capabilities and Pydantic for data validation.

## 🌟 Features

### 🤖 Multi-Agent Architecture
- **5 Specialized Agents**: Orchestrator, Web Scraping, Document Processing, Data Transformation, and Data Output
- **AI-Powered Coordination**: Lang<PERSON>hain integration for intelligent decision making
- **Scalable Design**: Easily extensible with new agent types

### 🖥️ Dual Interface System
- **Enhanced CLI**: Natural language processing, interactive modes, real-time progress
- **Web API**: RESTful endpoints, WebSocket support, comprehensive documentation
- **Real-time Monitoring**: Live agent status, job progress, system metrics

### 🛡️ Enterprise Features
- **Authentication & Authorization**: JWT-based security with role management
- **Rate Limiting**: Configurable limits to prevent abuse
- **Job Scheduling**: Priority-based queue with recurring tasks
- **Comprehensive Logging**: Detailed audit trails and error tracking

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [Usage Methods](#usage-methods)
- [Command-Line Reference](#command-line-reference)
- [Examples by Complexity](#examples-by-complexity)
  - [Basic Examples](#basic-examples)
  - [Intermediate Examples](#intermediate-examples)
  - [Advanced Examples](#advanced-examples)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Agent System](#agent-system)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
- [Development](#development)
- [Contributing](#contributing)
- [License](#license)

## 🚀 Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager
- Git

### Clone Repository

```bash
git clone https://github.com/MAg15TIq/web-scrapper.git
cd web-scrapper
```

### Install Dependencies

```bash
# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install additional system dependencies
python -m playwright install  # For JavaScript rendering
python -m spacy download en_core_web_sm  # For NLP features
```

### Environment Setup

```bash
# Copy environment template (if available)
cp .env.example .env

# Or create a new .env file with these variables:
# WEB_HOST=0.0.0.0
# WEB_PORT=8000
# WEB_DEBUG=false
# SECRET_KEY=your-super-secret-key-here
# OPENAI_API_KEY=your-openai-api-key  # Optional for AI features
# DATABASE_URL=sqlite:///./webscraper.db
# REDIS_URL=redis://localhost:6379/0  # Optional for advanced features
```

### System Requirements

**For JavaScript Rendering:**
- Playwright browsers will be installed automatically
- Requires ~200MB disk space for browser binaries

**For OCR Features (Optional):**
- **Ubuntu/Debian:** `sudo apt-get install tesseract-ocr`
- **macOS:** `brew install tesseract`
- **Windows:** Download from [Tesseract GitHub](https://github.com/UB-Mannheim/tesseract/wiki)

## ⚡ Quick Start

### 1. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers (required for JavaScript-heavy sites)
python -m playwright install

# Install spaCy language model (for NLP features)
python -m spacy download en_core_web_sm

# Create output directory
mkdir output
```

### 2. Basic Usage - Command Line

```bash
# Simple scraping with URL
python main.py scrape --url https://quotes.toscrape.com/

# Interactive mode
python main.py interactive

# Test basic functionality
python simple_test.py
```

### 3. Verify Installation

```bash
# Run the simple test to verify everything works
python simple_test.py

# Expected output: Successfully extracts quotes and saves to output/quotes.json

# Test the agent system
python examples/simple_scrape.py

# Test JavaScript rendering
python examples/javascript_example.py
```

## 🏗️ Architecture

### System Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Enhanced CLI  │    │   Web Frontend  │    │   Mobile App    │
│  (Natural Lang) │    │   (React/Vue)   │    │   (Future)      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      FastAPI Server       │
                    │   • Authentication        │
                    │   • Rate Limiting         │
                    │   • WebSocket Support     │
                    │   • Job Management        │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │   LangChain Framework     │
                    │   • AI Orchestration      │
                    │   • Natural Language      │
                    │   • Decision Making       │
                    └─────────────┬─────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
┌───────┴───────┐    ┌───────────┴────────────┐    ┌───────┴───────┐
│ Orchestrator  │    │    Specialized         │    │  Data Output  │
│    Agent      │    │      Agents            │    │    Agent      │
│               │    │ • Web Scraping         │    │               │
│ • Coordinates │    │ • Document Processing  │    │ • JSON Export │
│ • Plans       │    │ • Data Transformation  │    │ • CSV Export  │
│ • Monitors    │    │ • Error Recovery       │    │ • Database    │
└───────────────┘    └────────────────────────┘    └───────────────┘
```

### Agent Types

1. **Orchestrator Agent** 🎯
   - Coordinates all scraping operations
   - Plans execution strategies
   - Monitors system health

2. **Web Scraping Agent** 🕷️
   - Handles HTTP requests
   - Manages sessions and cookies
   - Implements anti-detection measures

3. **Document Processing Agent** 📄
   - Processes PDFs and documents
   - Extracts text and metadata
   - Handles various file formats

4. **Data Transformation Agent** 🔄
   - Cleans and normalizes data
   - Applies transformation rules
   - Validates data quality

5. **Data Output Agent** 💾
   - Manages data export
   - Supports multiple formats
   - Handles database operations

## 📖 Usage Methods

### 🎯 Five Ways to Use the Web Scraper

The Multi-Agent Web Scraping System offers **5 different approaches** to scrape websites, each suited for different use cases:

1. **🖥️ Command Line Interface (CLI)** - Direct commands for quick scraping
2. **🤖 Interactive Mode** - AI-powered conversational interface with guided setup
3. **🌐 Web API** - RESTful API for integration with other applications
4. **📝 Python Scripts** - Custom scripts using the agent framework directly
5. **⚡ Simple Test Mode** - Quick testing and validation without complex setup

---

### 1. 🖥️ Command Line Interface (CLI)

#### Basic Commands
```bash
# Scrape a single URL
python main.py scrape --url https://example.com

# Scrape with custom output file
python main.py scrape --url https://quotes.toscrape.com --output quotes_data.json

# List all available agents and their capabilities
python main.py agents

# Get help for any command
python main.py --help
python main.py scrape --help
```

#### Advanced CLI Options
```bash
# Scrape multiple pages with pagination
python main.py scrape --url https://example.com --max-pages 5

# Use specific agents for specialized tasks
python main.py scrape --url https://example.com --agents scraper,parser,storage

# Set custom delays and rate limiting
python main.py scrape --url https://example.com --delay 2 --rate-limit 10

# Enable verbose logging
python main.py scrape --url https://example.com --verbose
```

### 2. 🤖 Interactive Mode

```bash
# Start interactive session with guided prompts
python main.py interactive

# The system will guide you through:
# 1. URL input and validation
# 2. CSS selector configuration
# 3. Output format selection
# 4. Advanced options (JavaScript rendering, anti-detection)
# 5. Real-time progress monitoring
# 6. Results preview and export options
```

### 3. 🌐 Web API Mode

```bash
# Start the web server (if available)
python web/api/main.py

# API will be available at:
# - Main API: http://localhost:8000
# - Documentation: http://localhost:8000/api/docs
```

### 4. 📝 Python Script Integration

```python
# Create custom scraping scripts using the agent system
import asyncio
from agents.coordinator import CoordinatorAgent
from agents.scraper import ScraperAgent
from agents.parser import ParserAgent
from agents.storage import StorageAgent
from models.task import Task, TaskType

async def custom_scrape():
    # Initialize coordinator and agents
    coordinator = CoordinatorAgent()
    scraper = ScraperAgent(coordinator_id=coordinator.agent_id)
    parser = ParserAgent(coordinator_id=coordinator.agent_id)
    storage = StorageAgent(coordinator_id=coordinator.agent_id)

    # Register agents
    coordinator.register_agent(scraper)
    coordinator.register_agent(parser)
    coordinator.register_agent(storage)

    # Create and submit tasks
    task = Task(type=TaskType.FETCH_URL, parameters={"url": "https://example.com"})
    task_id = await coordinator.submit_task(task)

    # Wait for completion and get results
    # ... (see examples/simple_scrape.py for complete implementation)

# Run the async function
asyncio.run(custom_scrape())
```

### 5. ⚡ Simple Test Mode

```bash
# Quick test with built-in examples
python simple_test.py

# Test specific functionality
python examples/simple_scrape.py
python examples/advanced_scrape.py
python examples/javascript_example.py
```

---

## 🎯 Command-Line Reference

### Basic Commands

```bash
# Display help and available commands
python main.py --help
python main.py scrape --help

# Start interactive mode
python main.py interactive
```

### Scraping Commands

```bash
# Basic scraping
python main.py scrape --url "https://example.com"

# Scrape with custom output
python main.py scrape --url "https://example.com" --output "my_data.json"

# Specify output format
python main.py scrape --url "https://example.com" --format csv
python main.py scrape --url "https://example.com" --format excel
python main.py scrape --url "https://example.com" --format sqlite
```

### Advanced Options

```bash
# Multi-page scraping with pagination
python main.py scrape --url "https://example.com" --max-pages 5

# JavaScript rendering for dynamic content
python main.py scrape --url "https://spa-example.com" --render-js

# Anti-detection measures
python main.py scrape --url "https://example.com" --anti-detection

# Custom CSS selectors for data extraction
python main.py scrape --url "https://example.com" --selectors "title:h1,price:.price"

# Clean and normalize data
python main.py scrape --url "https://example.com" --clean-data

# Use all advanced features together
python main.py scrape \
  --url "https://example.com" \
  --selectors "title:h1,price:.price,description:.desc" \
  --max-pages 3 \
  --render-js \
  --anti-detection \
  --clean-data \
  --format csv \
  --output "scraped_data.csv"
```

### Configuration File Usage

```bash
# Use a configuration file
python main.py scrape --config examples/config.yaml
```

---

## 📚 Examples by Complexity

### 🟢 **BASIC Examples** - Getting Started

Perfect for beginners who want to understand the basic functionality. These examples require no configuration and work out of the box.

#### Example 1: Quick Start - Simple Quote Scraping
The fastest way to get started with the web scraper:

```bash
# Scrape quotes from a test website (no configuration needed)
python main.py scrape --url https://quotes.toscrape.com/

# Expected Output:
# ✅ 10 quotes extracted
# ✅ Authors and tags included
# ✅ Saved to output.json
```

**What this does:**
- Fetches the webpage content using the scraper agent
- Automatically detects quote elements using intelligent parsing
- Extracts text, author, and tags using default selectors
- Saves data in JSON format to the output directory

**Expected Result (output.json):**
```json
[
  {
    "text": "The world as we have created it is a process of our thinking...",
    "author": "Albert Einstein",
    "tags": ["change", "deep-thoughts", "thinking", "world"]
  },
  {
    "text": "It is our choices, Harry, that show what we truly are...",
    "author": "J.K. Rowling",
    "tags": ["abilities", "choices"]
  }
]
```

#### Example 2: Using the Simple Test Script
The simplest way to verify your installation:

```bash
# Run the built-in simple test (uses basic requests + BeautifulSoup)
python simple_test.py

# This demonstrates:
# ✅ Basic HTTP requests with requests library
# ✅ HTML parsing with BeautifulSoup
# ✅ Data extraction and JSON export
# ✅ Error handling and validation
# ✅ Creates output/quotes.json with structured data
```

**Code walkthrough (simple_test.py):**
<augment_code_snippet path="simple_test.py" mode="EXCERPT">
````python
import requests
from bs4 import BeautifulSoup
import json

def main():
    # Fetch the page
    url = "https://quotes.toscrape.com/"
    response = requests.get(url)

    # Parse HTML and extract quotes
    soup = BeautifulSoup(response.text, "html.parser")
    quotes = []
    for quote in soup.select(".quote"):
        quotes.append({
            "text": quote.select_one(".text").get_text(),
            "author": quote.select_one(".author").get_text(),
            "tags": [tag.get_text() for tag in quote.select(".tag")]
        })
````
</augment_code_snippet>

#### Example 3: Custom CSS Selectors
Learn how to target specific elements:

```bash
# Scrape with specific CSS selectors for precise data extraction
python main.py scrape \
  --url https://quotes.toscrape.com/ \
  --selectors "quote:.quote .text,author:.quote .author,tags:.quote .tags .tag" \
  --output quotes_custom.json

# This extracts:
# 📝 Quote text using .quote .text selector
# 👤 Author using .quote .author selector
# 🏷️ Tags using .quote .tags .tag selector
```

#### Example 4: Different Output Formats
Export data in various formats:

```bash
# Export as CSV for spreadsheet analysis
python main.py scrape \
  --url https://books.toscrape.com/ \
  --selectors "title:h3 a,price:.price_color,rating:.star-rating" \
  --format csv \
  --output books.csv

# Export as Excel for advanced analysis
python main.py scrape \
  --url https://books.toscrape.com/ \
  --selectors "title:h3 a,price:.price_color" \
  --format excel \
  --output books.xlsx

# Export to SQLite database
python main.py scrape \
  --url https://books.toscrape.com/ \
  --selectors "title:h3 a,price:.price_color" \
  --format sqlite \
  --output books.db
```

#### Example 5: Interactive Mode for Beginners
Let the system guide you through the process:

```bash
# Start interactive mode with step-by-step guidance
python main.py interactive

# The system will ask you:
# 1. 🌐 "What URL would you like to scrape?"
# 2. 🎯 "What data do you want to extract?" (with suggestions)
# 3. 📁 "What output format do you prefer?" (JSON/CSV/Excel)
# 4. ⚙️ "Do you need any advanced features?" (JavaScript/Anti-detection)
# 5. 🚀 Then it runs the scraping automatically
```

---

### 🟡 **INTERMEDIATE Examples** - Advanced Features

For users who need more sophisticated scraping capabilities with real-world scenarios.

#### Example 1: Multi-Page E-commerce Scraping
Scrape product catalogs across multiple pages:

```bash
# Scrape multiple pages with automatic pagination
python main.py scrape \
  --url https://books.toscrape.com/ \
  --selectors "title:h3 a,price:.price_color,rating:.star-rating" \
  --max-pages 5 \
  --format csv \
  --output books_catalog.csv

# Features used:
# 🔄 Automatic pagination (adds ?page=N parameter)
# 📊 Real-time progress tracking with Rich progress bars
# 💾 CSV output format for spreadsheet analysis
# 🎯 Custom CSS selectors for precise data extraction
# ⏱️ Built-in rate limiting to respect server resources
```

**What this does:**
- Automatically scrapes pages 1-5 by adding page parameters (?page=1, ?page=2, etc.)
- Extracts book titles, prices, and star ratings using CSS selectors
- Shows real-time progress with beautiful progress bars
- Combines all data into a single CSV file for analysis
- Implements automatic delays between requests

**Expected CSV Output:**
```csv
title,price,rating
"A Light in the Attic",£51.77,"Three"
"Tipping the Velvet",£53.74,"One"
"Soumission",£50.10,"One"
```

#### Example 2: JavaScript-Heavy Site Scraping
Handle dynamic content that loads with JavaScript:

```bash
# Scrape sites that require JavaScript execution
python main.py scrape \
  --url https://quotes.toscrape.com/js/ \
  --render-js \
  --selectors "quote:.quote .text,author:.quote .author" \
  --output js_quotes.json

# This handles:
# ⚡ JavaScript rendering with Playwright browser automation
# 🔄 Dynamic content loading and AJAX requests
# ⏳ Intelligent waiting for elements to appear
# 📸 Automatic screenshot capture for debugging
# 🖥️ Full browser environment simulation
```

**Advanced JavaScript Example with Custom Actions:**
```bash
# Run the comprehensive JavaScript example
python examples/javascript_example.py

# This example demonstrates:
# 🎭 Page rendering with Playwright
# 📜 Page scrolling to trigger lazy loading
# 🎨 Custom JavaScript execution for element manipulation
# 📸 Screenshot capture at different stages
# 🧹 Proper resource cleanup
```

#### Example 3: Anti-Detection Scraping
Bypass common bot detection mechanisms:

```bash
# Extract data with comprehensive anti-detection measures
python main.py scrape \
  --url https://quotes.toscrape.com/ \
  --selectors "quote:.quote .text,author:.quote .author" \
  --anti-detection \
  --clean-data \
  --output protected_quotes.json

# Anti-detection features:
# 🛡️ Dynamic browser fingerprint generation
# � User agent rotation and header randomization
# ⏱️ Human-like request timing patterns
# 🔍 Automatic bot detection checks
# 🧹 Data cleaning and normalization
# 🎭 Browser behavior simulation
```

#### Example 4: Configuration File-Based Scraping
Use YAML configuration for complex scraping scenarios:

```bash
# Use a configuration file for complex scraping setups
python main.py scrape --config examples/config.yaml

# The config file defines:
# 🎯 Multiple target URLs with different selectors
# ⚙️ Custom scraper settings (timeouts, retries)
# 📁 Output formats and file paths
# 🔄 Pagination and rate limiting rules
```

**Sample Configuration (examples/config.yaml):**
<augment_code_snippet path="examples/config.yaml" mode="EXCERPT">
````yaml
# Example configuration for scraping a product listing page
url: "https://example.com/products"
selectors:
  title: "h1.product-title"
  price: "span.product-price"
  description: "div.product-description"
  image: "img.product-image::attr(src)"
max_pages: 3
render_js: true
````
</augment_code_snippet>

#### Example 5: Data Processing Pipeline
Combine scraping with data transformation:

```bash
# Run the advanced example with data processing
python examples/advanced_scrape.py

# This comprehensive example includes:
# 🕷️ Multi-agent coordination workflow
# 🛡️ Anti-detection fingerprinting
# 📊 Data cleaning and transformation
# 🔍 Text analysis and sentiment detection
# 💾 Multiple output formats (JSON, CSV, Excel)
# 📈 Performance optimization techniques
```

---

### 🔴 **ADVANCED Examples** - Complex Scenarios

For power users who need enterprise-level scraping capabilities.

#### Example 1: Complete Agent System Workflow
```python
# File: advanced_workflow_example.py
import asyncio
from agents.coordinator import CoordinatorAgent
from agents.scraper import ScraperAgent
from agents.parser import ParserAgent
from agents.storage import StorageAgent
from agents.anti_detection import AntiDetectionAgent
from agents.data_transformation import DataTransformationAgent
from models.task import Task, TaskType

async def advanced_scraping_workflow():
    # Initialize coordinator and agents
    coordinator = CoordinatorAgent()
    scraper = ScraperAgent(coordinator_id=coordinator.agent_id)
    parser = ParserAgent(coordinator_id=coordinator.agent_id)
    storage = StorageAgent(coordinator_id=coordinator.agent_id)
    anti_detection = AntiDetectionAgent(coordinator_id=coordinator.agent_id)
    data_transformation = DataTransformationAgent(coordinator_id=coordinator.agent_id)

    # Register all agents
    for agent in [scraper, parser, storage, anti_detection, data_transformation]:
        coordinator.register_agent(agent)

    # Step 1: Generate fingerprint for anti-detection
    fingerprint_task = Task(
        type=TaskType.GENERATE_FINGERPRINT,
        parameters={"domain": "quotes.toscrape.com", "consistent": True}
    )
    fingerprint_id = await coordinator.submit_task(fingerprint_task)

    # Step 2: Check if site blocks scraping
    blocking_task = Task(
        type=TaskType.CHECK_BLOCKING,
        parameters={"url": "https://quotes.toscrape.com/", "check_methods": ["status_code"]}
    )
    blocking_id = await coordinator.submit_task(blocking_task)

    # Step 3: Scrape content
    scrape_task = Task(
        type=TaskType.FETCH_URL,
        parameters={"url": "https://quotes.toscrape.com/"}
    )
    scrape_id = await coordinator.submit_task(scrape_task)

    # Wait for scraping to complete and get result
    scrape_result = await wait_for_task_completion(coordinator, scrape_id)

    # Step 4: Parse content
    parse_task = Task(
        type=TaskType.PARSE_CONTENT,
        parameters={
            "content": scrape_result["content"],
            "selectors": {"quote": ".quote .text", "author": ".quote .author"}
        }
    )
    parse_id = await coordinator.submit_task(parse_task)
    parse_result = await wait_for_task_completion(coordinator, parse_id)

    # Step 5: Clean and transform data
    clean_task = Task(
        type=TaskType.CLEAN_DATA,
        parameters={
            "data": parse_result["extracted_data"],
            "operations": [{"field": "*", "operation": "strip_whitespace"}]
        }
    )
    clean_id = await coordinator.submit_task(clean_task)
    clean_result = await wait_for_task_completion(coordinator, clean_id)

    # Step 6: Store results
    store_task = Task(
        type=TaskType.STORE_DATA,
        parameters={
            "data": clean_result["data"],
            "format": "json",
            "path": "output/advanced_results.json"
        }
    )
    store_id = await coordinator.submit_task(store_task)
    await wait_for_task_completion(coordinator, store_id)

    print("Advanced workflow completed successfully!")

async def wait_for_task_completion(coordinator, task_id):
    while True:
        status = coordinator.get_task_status(task_id)
        if status and status["status"] in ["completed", "failed"]:
            if status["status"] == "failed":
                raise Exception(f"Task failed: {status['error']['message']}")
            return status["result"]
        await asyncio.sleep(0.1)

# Run: python advanced_workflow_example.py
if __name__ == "__main__":
    asyncio.run(advanced_scraping_workflow())
```

#### Example 2: JavaScript Rendering with Custom Actions
```python
# File: javascript_advanced_example.py
import asyncio
from agents.coordinator import CoordinatorAgent
from agents.javascript import JavaScriptAgent
from agents.parser import ParserAgent
from models.task import Task, TaskType

async def javascript_advanced_scraping():
    coordinator = CoordinatorAgent()
    js_agent = JavaScriptAgent(coordinator_id=coordinator.agent_id)
    parser = ParserAgent(coordinator_id=coordinator.agent_id)

    coordinator.register_agent(js_agent)
    coordinator.register_agent(parser)

    # Step 1: Render page with JavaScript
    render_task = Task(
        type=TaskType.RENDER_PAGE,
        parameters={
            "url": "https://quotes.toscrape.com/js/",
            "wait_for": ".quote",  # Wait for quotes to load
            "timeout": 30,
            "viewport": {"width": 1280, "height": 800}
        }
    )
    render_id = await coordinator.submit_task(render_task)
    render_result = await wait_for_task_completion(coordinator, render_id)

    page_id = render_result["page_id"]
    print(f"Page rendered, screenshot: {render_result['screenshot_path']}")

    # Step 2: Scroll page to load more content
    scroll_task = Task(
        type=TaskType.SCROLL_PAGE,
        parameters={
            "page_id": page_id,
            "max_scrolls": 3,
            "scroll_delay": 1.0
        }
    )
    scroll_id = await coordinator.submit_task(scroll_task)
    scroll_result = await wait_for_task_completion(coordinator, scroll_id)

    # Step 3: Execute custom JavaScript
    script_task = Task(
        type=TaskType.EXECUTE_SCRIPT,
        parameters={
            "page_id": page_id,
            "script": """
                // Highlight all quotes
                const quotes = document.querySelectorAll('.quote');
                quotes.forEach(quote => {
                    quote.style.border = '2px solid red';
                    quote.style.backgroundColor = 'lightyellow';
                });
                return quotes.length;
            """
        }
    )
    script_id = await coordinator.submit_task(script_task)
    script_result = await wait_for_task_completion(coordinator, script_id)

    print(f"Highlighted {script_result['result']} quotes")

    # Step 4: Take final screenshot
    screenshot_task = Task(
        type=TaskType.TAKE_SCREENSHOT,
        parameters={
            "page_id": page_id,
            "output_path": "output/highlighted_quotes.png",
            "full_page": True
        }
    )
    screenshot_id = await coordinator.submit_task(screenshot_task)
    screenshot_result = await wait_for_task_completion(coordinator, screenshot_id)

    # Step 5: Parse the final content
    parse_task = Task(
        type=TaskType.PARSE_CONTENT,
        parameters={
            "content": scroll_result["content"],
            "selectors": {"quote": ".quote .text", "author": ".quote .author"}
        }
    )
    parse_id = await coordinator.submit_task(parse_task)
    parse_result = await wait_for_task_completion(coordinator, parse_id)

    # Cleanup
    await js_agent.cleanup()

    print(f"Extracted {len(parse_result['extracted_data']['quote'])} quotes")
    print(f"Final screenshot: {screenshot_result['screenshot_path']}")

# Run: python javascript_advanced_example.py
if __name__ == "__main__":
    asyncio.run(javascript_advanced_scraping())
```

#### Example 3: Data Processing and Analysis Pipeline
```python
# File: data_analysis_pipeline.py
import asyncio
from agents.coordinator import CoordinatorAgent
from agents.scraper import ScraperAgent
from agents.parser import ParserAgent
from agents.data_transformation import DataTransformationAgent
from agents.storage import StorageAgent
from models.task import Task, TaskType

async def data_analysis_pipeline():
    # Initialize all agents
    coordinator = CoordinatorAgent()
    scraper = ScraperAgent(coordinator_id=coordinator.agent_id)
    parser = ParserAgent(coordinator_id=coordinator.agent_id)
    transformer = DataTransformationAgent(coordinator_id=coordinator.agent_id)
    storage = StorageAgent(coordinator_id=coordinator.agent_id)

    # Register agents
    for agent in [scraper, parser, transformer, storage]:
        coordinator.register_agent(agent)

    # Step 1: Scrape multiple pages of quotes
    all_content = []
    for page in range(1, 4):  # Scrape 3 pages
        url = f"https://quotes.toscrape.com/page/{page}/"

        scrape_task = Task(
            type=TaskType.FETCH_URL,
            parameters={"url": url}
        )
        scrape_id = await coordinator.submit_task(scrape_task)
        result = await wait_for_task_completion(coordinator, scrape_id)
        all_content.append(result["content"])

        print(f"Scraped page {page}")

    # Step 2: Parse all content
    all_parsed_data = []
    for i, content in enumerate(all_content):
        parse_task = Task(
            type=TaskType.PARSE_CONTENT,
            parameters={
                "content": content,
                "selectors": {
                    "quote": ".quote .text",
                    "author": ".quote .author",
                    "tags": ".quote .tags .tag"
                }
            }
        )
        parse_id = await coordinator.submit_task(parse_task)
        result = await wait_for_task_completion(coordinator, parse_id)
        all_parsed_data.append(result["extracted_data"])

        print(f"Parsed page {i+1}")

    # Step 3: Combine and structure data
    combined_quotes = []
    for page_data in all_parsed_data:
        for i, quote in enumerate(page_data.get("quote", [])):
            combined_quotes.append({
                "text": quote,
                "author": page_data.get("author", [])[i] if i < len(page_data.get("author", [])) else "Unknown",
                "tags": page_data.get("tags", [])[i] if i < len(page_data.get("tags", [])) else ""
            })

    # Step 4: Clean and analyze data
    clean_task = Task(
        type=TaskType.CLEAN_DATA,
        parameters={
            "data": combined_quotes,
            "operations": [
                {"field": "text", "operation": "strip_whitespace"},
                {"field": "author", "operation": "strip_whitespace"},
                {"field": "*", "operation": "remove_empty"}
            ],
            "add_metadata": True
        }
    )
    clean_id = await coordinator.submit_task(clean_task)
    clean_result = await wait_for_task_completion(coordinator, clean_id)

    # Step 5: Analyze text sentiment (if available)
    if clean_result["data"]:
        first_quote = clean_result["data"][0]["text"]
        analyze_task = Task(
            type=TaskType.ANALYZE_TEXT,
            parameters={
                "text": first_quote,
                "analyses": ["sentiment", "keywords"],
                "language": "en"
            }
        )
        analyze_id = await coordinator.submit_task(analyze_task)
        analyze_result = await wait_for_task_completion(coordinator, analyze_id)

        print(f"Sample analysis for first quote:")
        if "sentiment" in analyze_result["results"]:
            sentiment = analyze_result["results"]["sentiment"]
            print(f"  Sentiment: {sentiment.get('sentiment', 'N/A')}")

    # Step 6: Store results in multiple formats
    # JSON format
    json_task = Task(
        type=TaskType.STORE_DATA,
        parameters={
            "data": clean_result["data"],
            "format": "json",
            "path": "output/analyzed_quotes.json"
        }
    )
    json_id = await coordinator.submit_task(json_task)
    await wait_for_task_completion(coordinator, json_id)

    # CSV format
    csv_task = Task(
        type=TaskType.STORE_DATA,
        parameters={
            "data": clean_result["data"],
            "format": "csv",
            "path": "output/analyzed_quotes.csv"
        }
    )
    csv_id = await coordinator.submit_task(csv_task)
    await wait_for_task_completion(coordinator, csv_id)

    print(f"Pipeline completed! Processed {len(clean_result['data'])} quotes")
    print("Results saved to:")
    print("  - output/analyzed_quotes.json")
    print("  - output/analyzed_quotes.csv")

# Run: python data_analysis_pipeline.py
if __name__ == "__main__":
    asyncio.run(data_analysis_pipeline())
```

---

## ⚙️ Configuration

### Environment Variables

```bash
# Web Server
WEB_HOST=0.0.0.0
WEB_PORT=8000
WEB_DEBUG=false

# Security
SECRET_KEY=your-super-secret-key-here
OPENAI_API_KEY=your-openai-api-key

# Database
DATABASE_URL=sqlite:///./webscraper.db
REDIS_URL=redis://localhost:6379/0

# Features
WEB_ENABLE_AUTH=true
WEB_ENABLE_RATE_LIMITING=true
WEB_ENABLE_WEBSOCKETS=true
```

### Configuration Files

#### Main Configuration (config/defaults.yaml)
```yaml
# Scraper settings
scraper:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  follow_redirects: true
  verify_ssl: true

# Rate limiting
rate_limiting:
  enabled: true
  default_rate: 1  # Requests per period
  default_period: 2.0  # Period in seconds
  adaptive: true  # Adjust rate based on server responses

# Parser settings
parser:
  default_parser: "html.parser"  # Options: "html.parser", "lxml", "html5lib"
  normalize_whitespace: true
  extract_metadata: true

# Storage settings
storage:
  output_dir: "output"
  default_format: "json"  # Options: "json", "csv", "excel", "sqlite"
  pretty_json: true
  csv_delimiter: ","
  excel_engine: "openpyxl"

# Proxy settings (optional)
proxy:
  enabled: false
  rotation_enabled: true
  proxy_list_path: null  # Path to a file containing proxies
  check_interval: 600  # Seconds between proxy health checks
```

#### CLI Configuration
```yaml
# ~/.webscraper_cli/config.yaml
version: "1.0.0"
default_profile: "default"

profiles:
  default:
    name: "default"
    default_output_format: "json"
    auto_confirm: false
    theme: "default"

  production:
    name: "production"
    default_output_format: "csv"
    auto_confirm: true
    theme: "minimal"
    rate_limit: 0.5  # More conservative for production

logging:
  level: "INFO"
  file: "logs/cli.log"
  max_file_size: "10MB"
  backup_count: 5
```

#### Example Project Configuration
```yaml
# examples/config.yaml
project:
  name: "E-commerce Product Scraper"
  description: "Scrape product data from multiple e-commerce sites"

targets:
  - name: "books"
    url: "https://books.toscrape.com/"
    selectors:
      title: "h3 a"
      price: ".price_color"
      rating: ".star-rating"
    pagination:
      enabled: true
      max_pages: 5
      next_selector: ".next a"

  - name: "quotes"
    url: "https://quotes.toscrape.com/"
    selectors:
      text: ".quote .text"
      author: ".quote .author"
      tags: ".quote .tags .tag"

output:
  format: "json"
  path: "output/{name}_data.json"

settings:
  delay: 2.0
  user_agent: "Custom Scraper 1.0"
  respect_robots_txt: true
```

---

## 🌐 API Documentation

### Starting the Web API

```bash
# Start the development server
python web/api/main.py

# Or using uvicorn directly
uvicorn web.api.main:app --host 0.0.0.0 --port 8000 --reload

# API will be available at:
# - Main API: http://localhost:8000
# - Interactive docs: http://localhost:8000/docs
# - ReDoc docs: http://localhost:8000/redoc
```

### API Endpoints

#### Authentication
```bash
# Register a new user
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "email": "<EMAIL>", "password": "password"}'

# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "password": "password"}'
```

#### Job Management
```bash
# Create a scraping job
curl -X POST "http://localhost:8000/api/v1/jobs" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://quotes.toscrape.com/",
    "selectors": {
      "quote": ".quote .text",
      "author": ".quote .author"
    },
    "output_format": "json"
  }'

# Get job status
curl -X GET "http://localhost:8000/api/v1/jobs/{job_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"

# List all jobs
curl -X GET "http://localhost:8000/api/v1/jobs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Agent Management
```bash
# List available agents
curl -X GET "http://localhost:8000/api/v1/agents"

# Get agent details
curl -X GET "http://localhost:8000/api/v1/agents/{agent_id}"

# Update agent configuration
curl -X PUT "http://localhost:8000/api/v1/agents/{agent_id}" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"config": {"timeout": 30, "retries": 3}}'
```

### WebSocket Real-time Updates

```javascript
// Connect to WebSocket for real-time job updates
const ws = new WebSocket('ws://localhost:8000/ws/jobs');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Job update:', data);
    // Handle job status updates, progress, etc.
};

// Send commands via WebSocket
ws.send(JSON.stringify({
    "action": "start_job",
    "job_id": "12345"
}));
```

---

## 🤖 Agent System

### Available Agents

#### 1. **Coordinator Agent** 🎯
- **Purpose**: Orchestrates all scraping operations
- **Capabilities**: Task planning, agent coordination, resource management
- **Use Cases**: Complex multi-step scraping workflows

#### 2. **Scraper Agent** 🕷️
- **Purpose**: Handles HTTP requests and content fetching
- **Capabilities**: Session management, cookie handling, request optimization
- **Use Cases**: Basic web scraping, API interactions

#### 3. **JavaScript Agent** ⚡
- **Purpose**: Renders JavaScript-heavy pages
- **Capabilities**: Browser automation, dynamic content loading, screenshot capture
- **Use Cases**: SPAs, dynamic content, interactive pages

#### 4. **Parser Agent** 🔍
- **Purpose**: Extracts structured data from HTML/XML
- **Capabilities**: CSS selectors, XPath, regex patterns, data normalization
- **Use Cases**: Data extraction, content parsing, metadata extraction

#### 5. **Storage Agent** 💾
- **Purpose**: Manages data export and storage
- **Capabilities**: Multiple formats (JSON, CSV, Excel, SQLite), database integration
- **Use Cases**: Data persistence, format conversion, database operations

#### 6. **Authentication Agent** 🔐
- **Purpose**: Handles login and session management
- **Capabilities**: Form-based login, OAuth, session persistence, multi-factor auth
- **Use Cases**: Protected content, user-specific data, authenticated APIs

#### 7. **Anti-Detection Agent** 🛡️
- **Purpose**: Implements stealth and anti-detection measures
- **Capabilities**: User agent rotation, proxy management, fingerprint randomization
- **Use Cases**: Bot detection avoidance, large-scale scraping, protected sites

#### 8. **Data Transformation Agent** 🔄
- **Purpose**: Cleans and transforms extracted data
- **Capabilities**: Data cleaning, normalization, validation, enrichment
- **Use Cases**: Data quality assurance, format standardization, data enrichment

#### 9. **Error Recovery Agent** 🔧
- **Purpose**: Handles errors and implements recovery strategies
- **Capabilities**: Retry logic, fallback strategies, error analysis
- **Use Cases**: Robust scraping, error handling, system resilience

#### 10. **Monitoring Agent** 📊
- **Purpose**: Tracks system performance and health
- **Capabilities**: Metrics collection, alerting, performance optimization
- **Use Cases**: System monitoring, performance tuning, operational insights

---

## 🧪 Testing

### Run Tests
```bash
# Install test dependencies
pip install pytest pytest-cov pytest-asyncio

# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test categories
pytest tests/test_cli/
pytest tests/test_web_api/
pytest tests/test_integration/
```

### Manual Testing
```bash
# Test CLI functionality
python main.py --help
python main.py agents

# Test basic scraping
python simple_test.py

# Test advanced features
python examples/simple_scrape.py
python examples/advanced_scrape.py
python examples/javascript_example.py

# Test API health (if web server is running)
curl http://localhost:8000/health

# Test WebSocket connection
# Use a WebSocket client to connect to ws://localhost:8000/ws/test
```

---

## 📋 Best Practices

### Responsible Web Scraping

#### 1. **Respect robots.txt**
```bash
# Check robots.txt before scraping
curl https://example.com/robots.txt

# The scraper respects robots.txt by default in agent implementations
# Always check manually for important sites
```

#### 2. **Implement Rate Limiting**
```bash
# The system automatically implements delays between requests
# Use anti-detection for adaptive rate limiting
python main.py scrape --url "https://example.com" --anti-detection

# For manual control, the system optimizes request patterns automatically
```

#### 3. **Use Appropriate User Agents**
```python
# The anti-detection agent automatically handles user agent rotation
# Configure in your scraping tasks:
from models.task import Task, TaskType

fingerprint_task = Task(
    type=TaskType.GENERATE_FINGERPRINT,
    parameters={
        "domain": "example.com",
        "consistent": True  # Use consistent fingerprint for the session
    }
)
```

#### 4. **Handle Errors Gracefully**
```python
# The system includes built-in error handling
# Check task status for error information
task_status = coordinator.get_task_status(task_id)
if task_status["status"] == "failed":
    error_message = task_status["error"]["message"]
    print(f"Task failed: {error_message}")
    # Implement retry logic or fallback strategy
```

### Performance Optimization

#### 1. **Use the Agent System for Concurrent Processing**
```python
# The coordinator automatically handles concurrent task processing
# Submit multiple tasks and they will be processed efficiently
import asyncio
from agents.coordinator import CoordinatorAgent
from models.task import Task, TaskType

async def concurrent_scraping():
    coordinator = CoordinatorAgent()

    # Submit multiple scraping tasks
    urls = ["https://quotes.toscrape.com/page/1/",
            "https://quotes.toscrape.com/page/2/",
            "https://quotes.toscrape.com/page/3/"]

    task_ids = []
    for url in urls:
        task = Task(type=TaskType.FETCH_URL, parameters={"url": url})
        task_id = await coordinator.submit_task(task)
        task_ids.append(task_id)

    # Wait for all tasks to complete
    results = []
    for task_id in task_ids:
        while True:
            status = coordinator.get_task_status(task_id)
            if status and status["status"] in ["completed", "failed"]:
                if status["status"] == "completed":
                    results.append(status["result"])
                break
            await asyncio.sleep(0.1)

    return results
```

#### 2. **Optimize Selectors**
```bash
# Use specific, efficient CSS selectors in your commands
python main.py scrape \
  --url "https://example.com" \
  --selectors "title:h1,price:.price,desc:.description"

# Avoid overly complex selectors that slow down parsing
```

#### 3. **Use Anti-Detection for Optimal Request Patterns**
```bash
# The anti-detection agent optimizes request timing automatically
python main.py scrape \
  --url "https://example.com" \
  --anti-detection \
  --max-pages 5

# This automatically determines optimal delays and request patterns
```

### Data Quality

#### 1. **Use Built-in Data Cleaning**
```bash
# Enable automatic data cleaning and normalization
python main.py scrape \
  --url "https://example.com" \
  --selectors "title:h1,price:.price" \
  --clean-data \
  --output clean_data.json

# This automatically:
# - Strips whitespace
# - Removes empty entries
# - Adds metadata timestamps
```

#### 2. **Validate Data with Agent System**
```python
# Use the data transformation agent for validation
from models.task import Task, TaskType

# Clean and validate data
clean_task = Task(
    type=TaskType.CLEAN_DATA,
    parameters={
        "data": extracted_data,
        "operations": [
            {"field": "title", "operation": "strip_whitespace"},
            {"field": "price", "operation": "normalize_currency"},
            {"field": "*", "operation": "remove_empty"}
        ],
        "add_metadata": True
    }
)
```

#### 3. **Handle Missing Data with Multiple Selectors**
```bash
# Use comma-separated selectors for fallbacks
python main.py scrape \
  --url "https://example.com" \
  --selectors "title:h1,price:.price,.cost,.amount"

# The parser will try each selector until it finds data
```

---

## � Helper Functions for Custom Scripts

When creating custom Python scripts using the agent system, you'll often need these helper functions:

### Task Completion Helper
```python
# Add this helper function to your custom scripts
async def wait_for_task_completion(coordinator, task_id):
    """Wait for a task to complete and return its result."""
    while True:
        status = coordinator.get_task_status(task_id)
        if status and status["status"] in ["completed", "failed"]:
            if status["status"] == "failed":
                raise Exception(f"Task failed: {status['error']['message']}")
            return status["result"]
        await asyncio.sleep(0.1)
```

### Complete Script Template
```python
# File: my_custom_scraper.py
import asyncio
import os
from agents.coordinator import CoordinatorAgent
from agents.scraper import ScraperAgent
from agents.parser import ParserAgent
from agents.storage import StorageAgent
from models.task import Task, TaskType

async def wait_for_task_completion(coordinator, task_id):
    """Wait for a task to complete and return its result."""
    while True:
        status = coordinator.get_task_status(task_id)
        if status and status["status"] in ["completed", "failed"]:
            if status["status"] == "failed":
                raise Exception(f"Task failed: {status['error']['message']}")
            return status["result"]
        await asyncio.sleep(0.1)

async def my_custom_scraper():
    # Setup
    coordinator = CoordinatorAgent()
    scraper = ScraperAgent(coordinator_id=coordinator.agent_id)
    parser = ParserAgent(coordinator_id=coordinator.agent_id)
    storage = StorageAgent(coordinator_id=coordinator.agent_id)

    # Register agents
    for agent in [scraper, parser, storage]:
        coordinator.register_agent(agent)

    # Your scraping logic here
    url = "https://quotes.toscrape.com/"

    # Scrape
    scrape_task = Task(type=TaskType.FETCH_URL, parameters={"url": url})
    scrape_id = await coordinator.submit_task(scrape_task)
    scrape_result = await wait_for_task_completion(coordinator, scrape_id)

    # Parse
    parse_task = Task(
        type=TaskType.PARSE_CONTENT,
        parameters={
            "content": scrape_result["content"],
            "selectors": {"quote": ".quote .text", "author": ".quote .author"}
        }
    )
    parse_id = await coordinator.submit_task(parse_task)
    parse_result = await wait_for_task_completion(coordinator, parse_id)

    # Store
    os.makedirs("output", exist_ok=True)
    store_task = Task(
        type=TaskType.STORE_DATA,
        parameters={
            "data": parse_result["extracted_data"],
            "format": "json",
            "path": "output/my_custom_results.json"
        }
    )
    store_id = await coordinator.submit_task(store_task)
    await wait_for_task_completion(coordinator, store_id)

    print("Custom scraping completed!")

if __name__ == "__main__":
    asyncio.run(my_custom_scraper())
```

---

## �🔧 Troubleshooting

### Common Issues and Solutions

#### 1. **Installation Issues**

**Problem**: `pip install` fails with dependency conflicts
```bash
# Solution: Use a fresh virtual environment
python -m venv fresh_env
fresh_env\Scripts\activate  # Windows
pip install --upgrade pip
pip install -r requirements.txt
```

**Problem**: Playwright browsers not installing
```bash
# Solution: Install browsers manually
python -m playwright install
python -m playwright install-deps  # Linux only
```

#### 2. **Scraping Issues**

**Problem**: "403 Forbidden" or "Access Denied" errors
```bash
# Solution: Use anti-detection measures
python main.py scrape --url "https://example.com" --anti-detection

# Or configure custom user agent
python main.py scrape --url "https://example.com" --user-agent "Custom Bot 1.0"
```

**Problem**: JavaScript content not loading
```bash
# Solution: Enable JavaScript rendering
python main.py scrape --url "https://spa-site.com" --render-js

# Or increase wait time
python main.py scrape --url "https://spa-site.com" --render-js --wait-for-js 5
```

**Problem**: Rate limiting or IP blocking
```bash
# Solution: Implement delays and rotation
python main.py scrape --url "https://example.com" --delay 5 --anti-detection
```

#### 3. **Data Issues**

**Problem**: Empty or incomplete data extraction
```bash
# Solution: Use verbose logging to debug
python main.py scrape --url "https://example.com" --verbose

# Check and adjust CSS selectors
python main.py scrape --url "https://example.com" --selectors "title:.custom-title"
```

**Problem**: Encoding issues with special characters
```python
# Solution: Specify encoding in configuration
config = {
    "encoding": "utf-8",
    "normalize_unicode": True
}
```

#### 4. **Performance Issues**

**Problem**: Slow scraping performance
```bash
# Solution: Optimize settings
python main.py scrape --url "https://example.com" --timeout 10 --max-retries 2

# Use concurrent processing for multiple URLs
python examples/advanced_scrape.py  # Includes optimization examples
```

### Debug Mode

```bash
# Enable debug logging
export PYTHONPATH=.
python main.py scrape --url "https://example.com" --verbose --debug

# Check log files
tail -f logs/scraper.log
```

### Getting Help

1. **Check the documentation**: Review this README and example scripts
2. **Enable verbose logging**: Use `--verbose` flag for detailed output
3. **Test with simple sites**: Start with `https://quotes.toscrape.com/`
4. **Check configuration**: Verify your config files and environment variables
5. **Update dependencies**: Ensure you have the latest versions installed

## 🛠️ Development

### Project Structure
```
web-scrapper/
├── agents/                 # Agent implementations
├── cli/                   # Enhanced CLI components
├── web/                   # Web API and dashboard
├── config/                # Configuration files
├── models/                # Data models
├── monitoring/            # Monitoring components
├── tests/                 # Test suites
├── logs/                  # Log files
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

### Adding New Features

1. **New Agent Type**
   - Create agent class in `agents/`
   - Register with coordinator
   - Add configuration options

2. **New API Endpoint**
   - Add route in `web/api/routes/`
   - Update dependencies if needed
   - Add tests

3. **CLI Enhancement**
   - Extend command parser
   - Add new commands
   - Update help documentation

### Code Style
```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add tests for new functionality
- Update documentation
- Ensure all tests pass

---

## 📚 Additional Resources

### Learning Materials

- **Beginner Tutorial**: Start with `python simple_test.py`
- **Example Scripts**: Explore the `examples/` directory
- **Configuration Guide**: Check `config/defaults.yaml` for all options
- **API Documentation**: Visit `http://localhost:8000/docs` when running the web server

### External Documentation

- **BeautifulSoup**: [Beautiful Soup Documentation](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)
- **Playwright**: [Playwright Python Documentation](https://playwright.dev/python/)
- **FastAPI**: [FastAPI Documentation](https://fastapi.tiangolo.com/)
- **LangChain**: [LangChain Documentation](https://python.langchain.com/)

### Community and Support

- **GitHub Discussions**: Share ideas and ask questions
- **Example Gallery**: Community-contributed examples
- **Best Practices Guide**: Learn from experienced users

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [LangChain](https://langchain.com/) for AI orchestration and intelligent agent coordination
- [FastAPI](https://fastapi.tiangolo.com/) for the high-performance web framework
- [Rich](https://rich.readthedocs.io/) for beautiful and interactive CLI interfaces
- [Pydantic](https://pydantic-docs.helpmanual.io/) for robust data validation and settings management
- [Playwright](https://playwright.dev/) for reliable browser automation and JavaScript rendering
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) for HTML parsing and data extraction

## 📞 Support

- � **Issues**: [GitHub Issues](https://github.com/MAg15TIq/web-scrapper/issues)
- � **Discussions**: [GitHub Discussions](https://github.com/MAg15TIq/web-scrapper/discussions)
- 📖 **Documentation**: [Project Wiki](https://github.com/MAg15TIq/web-scrapper/wiki)
- 📧 **Email**: For enterprise support and custom solutions

### Quick Support Checklist

Before reporting issues:
1. ✅ Check this README for solutions
2. ✅ Try the troubleshooting section
3. ✅ Test with `python simple_test.py`
4. ✅ Enable verbose logging with `--verbose`
5. ✅ Check existing GitHub issues

---

**🚀 Happy Scraping!**

*Built with ❤️ for the web scraping community*
